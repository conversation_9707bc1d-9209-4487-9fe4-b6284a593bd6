import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import '../models/layout_model.dart';
import '../widgets/draggable_text.dart';
import '../widgets/draggable_heart.dart';

class EditorScreen extends StatefulWidget {
  final List<File> selectedImages;
  final LayoutModel selectedLayout;

  const EditorScreen({
    super.key,
    required this.selectedImages,
    required this.selectedLayout,
  });

  @override
  State<EditorScreen> createState() => _EditorScreenState();
}

class _EditorScreenState extends State<EditorScreen> {
  final GlobalKey _canvasKey = GlobalKey();
  
  // État du texte
  String _text = '';
  double _fontSize = 24.0;
  Color _textColor = Colors.black;
  FontWeight _fontWeight = FontWeight.normal;
  Offset _textPosition = const Offset(50, 50);
  bool _showText = false;
  
  // État du cœur
  bool _showHeart = false;
  Offset _heartPosition = const Offset(100, 100);
  Color _heartColor = Colors.red;
  double _heartSize = 30.0;
  
  // Contrôleurs
  final TextEditingController _textController = TextEditingController();
  
  @override
  void initState() {
    super.initState();
    _initializePositions();
  }
  
  void _initializePositions() {
    // Initialiser les positions par défaut basées sur le layout
    if (widget.selectedLayout.textZone != null) {
      final textZone = widget.selectedLayout.textZone!;
      _textPosition = Offset(
        textZone.x * 350, // Largeur approximative du canvas
        textZone.y * 500, // Hauteur approximative du canvas
      );
    }
    
    if (widget.selectedLayout.heartZone != null) {
      final heartZone = widget.selectedLayout.heartZone!;
      _heartPosition = Offset(
        heartZone.x * 350,
        heartZone.y * 500,
      );
      _heartSize = heartZone.size;
    }
  }
  
  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  void _showTextEditor() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Ajouter du texte',
          style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _textController,
              decoration: const InputDecoration(
                hintText: 'Entrez votre texte...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            
            // Taille de police
            Row(
              children: [
                const Text('Taille: '),
                Expanded(
                  child: Slider(
                    value: _fontSize,
                    min: 12,
                    max: 48,
                    divisions: 18,
                    label: _fontSize.round().toString(),
                    onChanged: (value) {
                      setState(() {
                        _fontSize = value;
                      });
                    },
                  ),
                ),
              ],
            ),
            
            // Couleur du texte
            Row(
              children: [
                const Text('Couleur: '),
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: _showColorPicker,
                  child: Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      color: _textColor,
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(color: Colors.grey),
                    ),
                  ),
                ),
              ],
            ),
            
            // Style de police
            Row(
              children: [
                const Text('Style: '),
                const SizedBox(width: 8),
                ToggleButtons(
                  isSelected: [_fontWeight == FontWeight.bold],
                  onPressed: (index) {
                    setState(() {
                      _fontWeight = _fontWeight == FontWeight.bold
                          ? FontWeight.normal
                          : FontWeight.bold;
                    });
                  },
                  children: const [
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 12),
                      child: Text('Gras'),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _text = _textController.text;
                _showText = _text.isNotEmpty;
              });
              Navigator.pop(context);
            },
            child: const Text('Ajouter'),
          ),
        ],
      ),
    );
  }
  
  void _showColorPicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choisir une couleur'),
        content: SingleChildScrollView(
          child: ColorPicker(
            pickerColor: _textColor,
            onColorChanged: (color) {
              setState(() {
                _textColor = color;
              });
            },
          ),
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
  
  void _toggleHeart() {
    setState(() {
      _showHeart = !_showHeart;
    });
  }
  
  void _showHeartOptions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Options du cœur',
          style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Taille du cœur
            Row(
              children: [
                const Text('Taille: '),
                Expanded(
                  child: Slider(
                    value: _heartSize,
                    min: 20,
                    max: 60,
                    divisions: 8,
                    label: _heartSize.round().toString(),
                    onChanged: (value) {
                      setState(() {
                        _heartSize = value;
                      });
                    },
                  ),
                ),
              ],
            ),
            
            // Couleur du cœur
            Row(
              children: [
                const Text('Couleur: '),
                const SizedBox(width: 8),
                ...[
                  Colors.red,
                  Colors.pink,
                  Colors.purple,
                  Colors.orange,
                  Colors.blue,
                ].map((color) => GestureDetector(
                  onTap: () {
                    setState(() {
                      _heartColor = color;
                    });
                  },
                  child: Container(
                    width: 30,
                    height: 30,
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: _heartColor == color
                            ? Colors.black
                            : Colors.transparent,
                        width: 2,
                      ),
                    ),
                    child: const Icon(
                      Icons.favorite,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                )),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }
  
  Future<void> _saveCollage() async {
    try {
      // Capturer le widget comme image
      RenderRepaintBoundary boundary = _canvasKey.currentContext!
          .findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      Uint8List pngBytes = byteData!.buffer.asUint8List();
      
      // Sauvegarder le fichier
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final file = File('${directory.path}/collage_$timestamp.png');
      await file.writeAsBytes(pngBytes);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Collage sauvegardé: ${file.path}'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'Partager',
              textColor: Colors.white,
              onPressed: () => _shareCollage(file.path),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la sauvegarde: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  Future<void> _shareCollage(String filePath) async {
    try {
      await Share.shareXFiles(
        [XFile(filePath)],
        text: 'Mon collage créé avec HCP Layout Maker',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du partage: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: Text(
          'Éditeur de collage',
          style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.grey[800],
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _saveCollage,
            icon: const Icon(Icons.save),
            tooltip: 'Sauvegarder',
          ),
        ],
      ),
      body: Column(
        children: [
          // Canvas de l'éditeur
          Expanded(
            child: Center(
              child: Container(
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.2),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: RepaintBoundary(
                    key: _canvasKey,
                    child: _buildCanvas(),
                  ),
                ),
              ),
            ),
          ),
          
          // Barre d'outils
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildToolButton(
                  icon: Icons.text_fields,
                  label: 'Texte',
                  onTap: _showTextEditor,
                  isActive: _showText,
                ),
                _buildToolButton(
                  icon: Icons.favorite,
                  label: 'Cœur',
                  onTap: _toggleHeart,
                  isActive: _showHeart,
                  onLongPress: _showHeartOptions,
                ),
                _buildToolButton(
                  icon: Icons.save_alt,
                  label: 'Sauver',
                  onTap: _saveCollage,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildCanvas() {
    return Container(
      width: 350,
      height: 500,
      color: Colors.white,
      child: Stack(
        children: [
          // Images dans les zones définies
          ...widget.selectedLayout.photoZones.asMap().entries.map((entry) {
            final index = entry.key;
            final zone = entry.value;
            
            if (index < widget.selectedImages.length) {
              return Positioned(
                left: zone.x * 350,
                top: zone.y * 500,
                width: zone.width * 350,
                height: zone.height * 500,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(zone.borderRadius ?? 8),
                  child: Image.file(
                    widget.selectedImages[index],
                    fit: BoxFit.cover,
                  ),
                ),
              );
            }
            return const SizedBox.shrink();
          }),
          
          // Texte draggable
          if (_showText)
            DraggableText(
              text: _text,
              fontSize: _fontSize,
              color: _textColor,
              fontWeight: _fontWeight,
              initialPosition: _textPosition,
              onPositionChanged: (position) {
                _textPosition = position;
              },
            ),
          
          // Cœur draggable
          if (_showHeart)
            DraggableHeart(
              color: _heartColor,
              size: _heartSize,
              initialPosition: _heartPosition,
              onPositionChanged: (position) {
                _heartPosition = position;
              },
            ),
        ],
      ),
    );
  }
  
  Widget _buildToolButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool isActive = false,
    VoidCallback? onLongPress,
  }) {
    return GestureDetector(
      onTap: onTap,
      onLongPress: onLongPress,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isActive
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isActive
                ? Theme.of(context).colorScheme.primary
                : Colors.grey[300]!,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isActive
                  ? Theme.of(context).colorScheme.primary
                  : Colors.grey[600],
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: isActive
                    ? Theme.of(context).colorScheme.primary
                    : Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }
}