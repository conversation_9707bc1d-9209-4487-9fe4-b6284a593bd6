import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import '../models/layout_model.dart';

class CustomLayoutService {
  static const String _customLayoutsPath = 'assets/custom_layouts/';
  
  /// Récupère tous les layouts personnalisés disponibles
  static Future<List<LayoutModel>> getCustomLayouts() async {
    List<LayoutModel> customLayouts = [];
    
    try {
      // Liste des fichiers PNG dans le dossier custom_layouts
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final Map<String, dynamic> manifestMap = 
          Map<String, dynamic>.from(json.decode(manifestContent));
      
      final customLayoutFiles = manifestMap.keys
          .where((String key) => key.startsWith(_customLayoutsPath))
          .where((String key) => key.endsWith('.png') || key.endsWith('.svg'))
          .toList();
      
      for (String filePath in customLayoutFiles) {
        final fileName = filePath.split('/').last;
        final layoutName = fileName
            .replaceAll('.png', '')
            .replaceAll('.svg', '')
            .replaceAll('_', ' ');
        
        // Détermine le nombre de photos basé sur le nom du fichier
        int photoCount = _extractPhotoCountFromFileName(fileName);
        
        customLayouts.add(LayoutModel(
          id: 'custom_${fileName.replaceAll('.png', '').replaceAll('.svg', '')}',
          name: layoutName,
          requiredPhotos: photoCount,
          imagePath: filePath,
          photoZones: _generateDefaultPhotoZones(photoCount),
          textZone: _generateDefaultTextZone(),
          heartZone: _generateDefaultHeartZone(),
        ));
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement des layouts personnalisés: $e');
    }
    
    return customLayouts;
  }
  
  /// Extrait le nombre de photos du nom de fichier
  static int _extractPhotoCountFromFileName(String fileName) {
    // Recherche un nombre dans le nom du fichier
    final RegExp numberRegex = RegExp(r'(\d+)');
    final match = numberRegex.firstMatch(fileName);
    
    if (match != null) {
      return int.tryParse(match.group(1)!) ?? 2;
    }
    
    // Valeur par défaut si aucun nombre n'est trouvé
    return 2;
  }
  
  /// Génère des zones de photos par défaut
  static List<PhotoZone> _generateDefaultPhotoZones(int photoCount) {
    List<PhotoZone> zones = [];
    
    // Génère des zones de photos avec une disposition simple
    double zoneWidth = 1.0 / (photoCount <= 2 ? photoCount : 2);
    double zoneHeight = photoCount <= 2 ? 0.7 : 0.35;
    
    for (int i = 0; i < photoCount; i++) {
      double x = (i % 2) * zoneWidth;
      double y = (i ~/ 2) * zoneHeight;
      
      zones.add(PhotoZone(
        x: x,
        y: y,
        width: zoneWidth,
        height: zoneHeight,
      ));
    }
    
    return zones;
  }
  
  /// Génère une zone de texte par défaut
  static TextZone _generateDefaultTextZone() {
    return TextZone(
      x: 0.1,
      y: 0.8,
      width: 0.8,
      height: 0.1,
    );
  }
  
  /// Génère une zone de cœur par défaut
  static HeartZone _generateDefaultHeartZone() {
    return HeartZone(
      x: 0.85,
      y: 0.05,
      size: 0.1,
    );
  }
}