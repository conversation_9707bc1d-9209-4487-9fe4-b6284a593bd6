import '../models/layout_model.dart';
import 'custom_layout_service.dart';

class LayoutService {
  static List<LayoutModel> getAvailableLayouts() {
    return [
      // Layout pour 2 photos - Côte à côte
      const LayoutModel(
        id: 'layout_2_side_by_side',
        name: 'Côte à côte',
        imagePath: 'assets/layouts/layout_2_side_by_side.svg',
        requiredPhotos: 2,
        photoZones: [
          PhotoZone(x: 0.05, y: 0.1, width: 0.4, height: 0.8, borderRadius: 12),
          PhotoZone(x: 0.55, y: 0.1, width: 0.4, height: 0.8, borderRadius: 12),
        ],
        textZone: TextZone(x: 0.1, y: 0.92, width: 0.8, height: 0.06),
        heartZone: HeartZone(x: 0.85, y: 0.05, size: 30),
      ),
      
      // Layout pour 2 photos - Vertical
      const LayoutModel(
        id: 'layout_2_vertical',
        name: 'Vertical',
        imagePath: 'assets/layouts/layout_2_vertical.svg',
        requiredPhotos: 2,
        photoZones: [
          PhotoZone(x: 0.1, y: 0.05, width: 0.8, height: 0.4, borderRadius: 12),
          PhotoZone(x: 0.1, y: 0.55, width: 0.8, height: 0.4, borderRadius: 12),
        ],
        textZone: TextZone(x: 0.1, y: 0.47, width: 0.8, height: 0.06),
        heartZone: HeartZone(x: 0.85, y: 0.02, size: 25),
      ),
      
      // Layout pour 3 photos - Triangle
      const LayoutModel(
        id: 'layout_3_triangle',
        name: 'Triangle',
        imagePath: 'assets/layouts/layout_3_triangle.svg',
        requiredPhotos: 3,
        photoZones: [
          PhotoZone(x: 0.25, y: 0.05, width: 0.5, height: 0.35, borderRadius: 12),
          PhotoZone(x: 0.05, y: 0.5, width: 0.4, height: 0.35, borderRadius: 12),
          PhotoZone(x: 0.55, y: 0.5, width: 0.4, height: 0.35, borderRadius: 12),
        ],
        textZone: TextZone(x: 0.1, y: 0.9, width: 0.8, height: 0.08),
        heartZone: HeartZone(x: 0.85, y: 0.02, size: 25),
      ),
      
      // Layout pour 3 photos - Ligne
      const LayoutModel(
        id: 'layout_3_line',
        name: 'En ligne',
        imagePath: 'assets/layouts/layout_3_line.svg',
        requiredPhotos: 3,
        photoZones: [
          PhotoZone(x: 0.02, y: 0.15, width: 0.3, height: 0.7, borderRadius: 8),
          PhotoZone(x: 0.35, y: 0.15, width: 0.3, height: 0.7, borderRadius: 8),
          PhotoZone(x: 0.68, y: 0.15, width: 0.3, height: 0.7, borderRadius: 8),
        ],
        textZone: TextZone(x: 0.1, y: 0.9, width: 0.8, height: 0.08),
        heartZone: HeartZone(x: 0.85, y: 0.02, size: 25),
      ),
      
      // Layout pour 4 photos - Grille
      const LayoutModel(
        id: 'layout_4_grid',
        name: 'Grille 2x2',
        imagePath: 'assets/layouts/layout_4_grid.svg',
        requiredPhotos: 4,
        photoZones: [
          PhotoZone(x: 0.05, y: 0.1, width: 0.4, height: 0.35, borderRadius: 8),
          PhotoZone(x: 0.55, y: 0.1, width: 0.4, height: 0.35, borderRadius: 8),
          PhotoZone(x: 0.05, y: 0.55, width: 0.4, height: 0.35, borderRadius: 8),
          PhotoZone(x: 0.55, y: 0.55, width: 0.4, height: 0.35, borderRadius: 8),
        ],
        textZone: TextZone(x: 0.1, y: 0.92, width: 0.8, height: 0.06),
        heartZone: HeartZone(x: 0.85, y: 0.02, size: 25),
      ),
      
      // Layout pour 4 photos - Focus central
      const LayoutModel(
        id: 'layout_4_focus',
        name: 'Focus central',
        imagePath: 'assets/layouts/layout_4_focus.svg',
        requiredPhotos: 4,
        photoZones: [
          PhotoZone(x: 0.25, y: 0.05, width: 0.5, height: 0.4, borderRadius: 12),
          PhotoZone(x: 0.05, y: 0.55, width: 0.25, height: 0.25, borderRadius: 8),
          PhotoZone(x: 0.375, y: 0.55, width: 0.25, height: 0.25, borderRadius: 8),
          PhotoZone(x: 0.7, y: 0.55, width: 0.25, height: 0.25, borderRadius: 8),
        ],
        textZone: TextZone(x: 0.1, y: 0.85, width: 0.8, height: 0.08),
        heartZone: HeartZone(x: 0.85, y: 0.02, size: 25),
      ),
      
      // Layout pour 5 photos - Pyramide
      const LayoutModel(
        id: 'layout_5_pyramid',
        name: 'Pyramide',
        imagePath: 'assets/layouts/layout_5_pyramid.svg',
        requiredPhotos: 5,
        photoZones: [
          PhotoZone(x: 0.375, y: 0.05, width: 0.25, height: 0.25, borderRadius: 8),
          PhotoZone(x: 0.125, y: 0.35, width: 0.25, height: 0.25, borderRadius: 8),
          PhotoZone(x: 0.625, y: 0.35, width: 0.25, height: 0.25, borderRadius: 8),
          PhotoZone(x: 0.05, y: 0.65, width: 0.25, height: 0.25, borderRadius: 8),
          PhotoZone(x: 0.7, y: 0.65, width: 0.25, height: 0.25, borderRadius: 8),
        ],
        textZone: TextZone(x: 0.3, y: 0.65, width: 0.4, height: 0.25),
        heartZone: HeartZone(x: 0.85, y: 0.02, size: 25),
      ),
      
      // Layout pour 5 photos - Croix
      const LayoutModel(
        id: 'layout_5_cross',
        name: 'Croix',
        imagePath: 'assets/layouts/layout_5_cross.svg',
        requiredPhotos: 5,
        photoZones: [
          PhotoZone(x: 0.375, y: 0.05, width: 0.25, height: 0.25, borderRadius: 8),
          PhotoZone(x: 0.05, y: 0.375, width: 0.25, height: 0.25, borderRadius: 8),
          PhotoZone(x: 0.375, y: 0.375, width: 0.25, height: 0.25, borderRadius: 8),
          PhotoZone(x: 0.7, y: 0.375, width: 0.25, height: 0.25, borderRadius: 8),
          PhotoZone(x: 0.375, y: 0.7, width: 0.25, height: 0.25, borderRadius: 8),
        ],
        textZone: TextZone(x: 0.1, y: 0.32, width: 0.2, height: 0.06),
        heartZone: HeartZone(x: 0.85, y: 0.02, size: 25),
      ),
    ];
  }
  
  static Future<List<LayoutModel>> getLayoutsForPhotoCount(int photoCount) async {
    final allLayouts = getAvailableLayouts();
    final customLayouts = await CustomLayoutService.getCustomLayouts();
    
    final predefinedLayouts = allLayouts.where((layout) => layout.requiredPhotos == photoCount).toList();
    final filteredCustomLayouts = customLayouts.where((layout) => layout.requiredPhotos == photoCount).toList();
    
    // Combine les layouts prédéfinis et personnalisés
    return [...predefinedLayouts, ...filteredCustomLayouts];
  }
  
  static LayoutModel? getLayoutById(String id) {
    try {
      return getAvailableLayouts().firstWhere((layout) => layout.id == id);
    } catch (e) {
      return null;
    }
  }
}