<svg width="400" height="400" viewBox="0 0 400 400" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="400" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
  
  <!-- Photo Zone 1 -->
  <rect x="20" y="20" width="170" height="170" fill="#e9ecef" stroke="#6c757d" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="105" y="110" text-anchor="middle" font-family="Arial" font-size="14" fill="#6c757d">Photo 1</text>
  
  <!-- Photo Zone 2 -->
  <rect x="210" y="20" width="170" height="170" fill="#e9ecef" stroke="#6c757d" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="295" y="110" text-anchor="middle" font-family="Arial" font-size="14" fill="#6c757d">Photo 2</text>
  
  <!-- Decorative border -->
  <rect x="10" y="10" width="380" height="380" fill="none" stroke="#fd7e14" stroke-width="3" rx="10"/>
  
  <!-- Text Zone Indicator -->
  <rect x="40" y="320" width="320" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" stroke-dasharray="3,3" rx="5"/>
  <text x="200" y="345" text-anchor="middle" font-family="Arial" font-size="12" fill="#856404">Zone de Texte</text>
  
  <!-- Heart Zone Indicator -->
  <circle cx="350" cy="50" r="20" fill="#f8d7da" stroke="#dc3545" stroke-width="1" stroke-dasharray="3,3"/>
  <text x="350" y="55" text-anchor="middle" font-family="Arial" font-size="10" fill="#721c24">♥</text>
  
  <!-- Title -->
  <text x="200" y="280" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="#495057">Layout Exemple - 2 Photos</text>
</svg>