class LayoutModel {
  final String id;
  final String name;
  final String imagePath;
  final int requiredPhotos;
  final List<PhotoZone> photoZones;
  final TextZone? textZone;
  final HeartZone? heartZone;

  const LayoutModel({
    required this.id,
    required this.name,
    required this.imagePath,
    required this.requiredPhotos,
    required this.photoZones,
    this.textZone,
    this.heartZone,
  });

  factory LayoutModel.fromJson(Map<String, dynamic> json) {
    return LayoutModel(
      id: json['id'],
      name: json['name'],
      imagePath: json['imagePath'],
      requiredPhotos: json['requiredPhotos'],
      photoZones: (json['photoZones'] as List)
          .map((zone) => PhotoZone.fromJson(zone))
          .toList(),
      textZone: json['textZone'] != null
          ? TextZone.fromJson(json['textZone'])
          : null,
      heartZone: json['heartZone'] != null
          ? HeartZone.fromJson(json['heartZone'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'imagePath': imagePath,
      'requiredPhotos': requiredPhotos,
      'photoZones': photoZones.map((zone) => zone.toJson()).toList(),
      'textZone': textZone?.toJson(),
      'heartZone': heartZone?.toJson(),
    };
  }
}

class PhotoZone {
  final double x;
  final double y;
  final double width;
  final double height;
  final double? borderRadius;

  const PhotoZone({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    this.borderRadius,
  });

  factory PhotoZone.fromJson(Map<String, dynamic> json) {
    return PhotoZone(
      x: json['x'].toDouble(),
      y: json['y'].toDouble(),
      width: json['width'].toDouble(),
      height: json['height'].toDouble(),
      borderRadius: json['borderRadius']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
      'width': width,
      'height': height,
      'borderRadius': borderRadius,
    };
  }
}

class TextZone {
  final double x;
  final double y;
  final double width;
  final double height;

  const TextZone({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });

  factory TextZone.fromJson(Map<String, dynamic> json) {
    return TextZone(
      x: json['x'].toDouble(),
      y: json['y'].toDouble(),
      width: json['width'].toDouble(),
      height: json['height'].toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
      'width': width,
      'height': height,
    };
  }
}

class HeartZone {
  final double x;
  final double y;
  final double size;

  const HeartZone({
    required this.x,
    required this.y,
    required this.size,
  });

  factory HeartZone.fromJson(Map<String, dynamic> json) {
    return HeartZone(
      x: json['x'].toDouble(),
      y: json['y'].toDouble(),
      size: json['size'].toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
      'size': size,
    };
  }
}