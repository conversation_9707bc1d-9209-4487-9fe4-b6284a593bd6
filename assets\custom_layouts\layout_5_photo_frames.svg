<svg width="500" height="800" viewBox="0 0 500 800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#FF8C00;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DAA520;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="rgba(0,0,0,0.3)"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="500" height="800" fill="#f8f8f8"/>
  
  <!-- Photo Frame 1 (top) -->
  <g transform="translate(150,50) rotate(15)">
    <rect x="0" y="0" width="120" height="120" fill="url(#goldGradient)" stroke="#B8860B" stroke-width="3" filter="url(#shadow)"/>
    <rect x="10" y="10" width="100" height="100" fill="white" stroke="#ddd" stroke-width="1"/>
    <!-- Decorative corners -->
    <circle cx="5" cy="5" r="3" fill="#FFD700"/>
    <circle cx="115" cy="5" r="3" fill="#FFD700"/>
    <circle cx="5" cy="115" r="3" fill="#FFD700"/>
    <circle cx="115" cy="115" r="3" fill="#FFD700"/>
  </g>
  
  <!-- Photo Frame 2 (second) -->
  <g transform="translate(250,180) rotate(-10)">
    <rect x="0" y="0" width="120" height="120" fill="url(#goldGradient)" stroke="#B8860B" stroke-width="3" filter="url(#shadow)"/>
    <rect x="10" y="10" width="100" height="100" fill="white" stroke="#ddd" stroke-width="1"/>
    <!-- Decorative corners -->
    <circle cx="5" cy="5" r="3" fill="#FFD700"/>
    <circle cx="115" cy="5" r="3" fill="#FFD700"/>
    <circle cx="5" cy="115" r="3" fill="#FFD700"/>
    <circle cx="115" cy="115" r="3" fill="#FFD700"/>
  </g>
  
  <!-- Photo Frame 3 (middle) -->
  <g transform="translate(80,320) rotate(8)">
    <rect x="0" y="0" width="120" height="120" fill="url(#goldGradient)" stroke="#B8860B" stroke-width="3" filter="url(#shadow)"/>
    <rect x="10" y="10" width="100" height="100" fill="white" stroke="#ddd" stroke-width="1"/>
    <!-- Decorative corners -->
    <circle cx="5" cy="5" r="3" fill="#FFD700"/>
    <circle cx="115" cy="5" r="3" fill="#FFD700"/>
    <circle cx="5" cy="115" r="3" fill="#FFD700"/>
    <circle cx="115" cy="115" r="3" fill="#FFD700"/>
  </g>
  
  <!-- Photo Frame 4 (fourth) -->
  <g transform="translate(280,450) rotate(-12)">
    <rect x="0" y="0" width="120" height="120" fill="url(#goldGradient)" stroke="#B8860B" stroke-width="3" filter="url(#shadow)"/>
    <rect x="10" y="10" width="100" height="100" fill="white" stroke="#ddd" stroke-width="1"/>
    <!-- Decorative corners -->
    <circle cx="5" cy="5" r="3" fill="#FFD700"/>
    <circle cx="115" cy="5" r="3" fill="#FFD700"/>
    <circle cx="5" cy="115" r="3" fill="#FFD700"/>
    <circle cx="115" cy="115" r="3" fill="#FFD700"/>
  </g>
  
  <!-- Photo Frame 5 (bottom) -->
  <g transform="translate(120,580) rotate(5)">
    <rect x="0" y="0" width="120" height="120" fill="url(#goldGradient)" stroke="#B8860B" stroke-width="3" filter="url(#shadow)"/>
    <rect x="10" y="10" width="100" height="100" fill="white" stroke="#ddd" stroke-width="1"/>
    <!-- Decorative corners -->
    <circle cx="5" cy="5" r="3" fill="#FFD700"/>
    <circle cx="115" cy="5" r="3" fill="#FFD700"/>
    <circle cx="5" cy="115" r="3" fill="#FFD700"/>
    <circle cx="115" cy="115" r="3" fill="#FFD700"/>
  </g>
  
  <!-- Heart decoration -->
  <g transform="translate(50,400)">
    <path d="M12,21.35l-1.45-1.32C5.4,15.36,2,12.28,2,8.5 C2,5.42,4.42,3,7.5,3c1.74,0,3.41,0.81,4.5,2.09C13.09,3.81,14.76,3,16.5,3 C19.58,3,22,5.42,22,8.5c0,3.78-3.4,6.86-8.55,11.54L12,21.35z" fill="#FF0000" transform="scale(1.5)"/>
  </g>
</svg>