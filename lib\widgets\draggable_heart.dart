import 'package:flutter/material.dart';

class DraggableHeart extends StatefulWidget {
  final Color color;
  final double size;
  final Offset initialPosition;
  final Function(Offset) onPositionChanged;

  const DraggableHeart({
    super.key,
    required this.color,
    required this.size,
    required this.initialPosition,
    required this.onPositionChanged,
  });

  @override
  State<DraggableHeart> createState() => _DraggableHeartState();
}

class _DraggableHeartState extends State<DraggableHeart>
    with SingleTickerProviderStateMixin {
  late Offset _position;
  bool _isDragging = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _position = widget.initialPosition;
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(DraggableHeart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initialPosition != widget.initialPosition) {
      _position = widget.initialPosition;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: _position.dx,
      top: _position.dy,
      child: GestureDetector(
        onPanStart: (details) {
          setState(() {
            _isDragging = true;
          });
          _animationController.forward();
        },
        onPanUpdate: (details) {
          setState(() {
            _position = Offset(
              (_position.dx + details.delta.dx).clamp(0.0, 320.0),
              (_position.dy + details.delta.dy).clamp(0.0, 470.0),
            );
          });
          widget.onPositionChanged(_position);
        },
        onPanEnd: (details) {
          setState(() {
            _isDragging = false;
          });
          _animationController.reverse();
        },
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _isDragging ? _scaleAnimation.value : 1.0,
              child: Container(
                width: widget.size,
                height: widget.size,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _isDragging
                      ? Colors.white.withOpacity(0.8)
                      : Colors.transparent,
                  border: _isDragging
                      ? Border.all(color: Colors.blue, width: 2)
                      : null,
                ),
                child: Center(
                  child: Icon(
                    Icons.favorite,
                    color: widget.color,
                    size: widget.size * 0.8,
                    shadows: [
                      Shadow(
                        color: Colors.black.withOpacity(0.3),
                        offset: const Offset(1, 1),
                        blurRadius: 2,
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}